"""
Python client for the Shapefile Conversion API
Usage example for desktop applications
"""

import requests
import os
import json
from pathlib import Path
from typing import Optional, Dict, Any
import zipfile
import tempfile
import shutil


class ShapefileConverterClient:
    """Client for interacting with the Shapefile Conversion API"""
    
    def __init__(self, api_url: str = "http://localhost:3000"):
        """
        Initialize the client with API URL
        
        Args:
            api_url: Base URL of the API server
        """
        self.api_url = api_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """
        Check if the API is running and healthy
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            response = self.session.get(f"{self.api_url}/api/health")
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def analyze_shapefile(self, shapefile_path: str) -> Optional[Dict[str, Any]]:
        """
        Analyze a shapefile without converting it
        
        Args:
            shapefile_path: Path to the zipped shapefile
            
        Returns:
            Analysis results or None if failed
        """
        try:
            with open(shapefile_path, 'rb') as f:
                files = {'shapefile': f}
                response = self.session.post(
                    f"{self.api_url}/api/analyze",
                    files=files
                )
                
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Analysis failed: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"Error analyzing shapefile: {e}")
            return None
    
    def convert_to_multipart(self, 
                            input_path: str, 
                            output_path: Optional[str] = None,
                            timeout: int = 300) -> bool:
        """
        Convert single-part shapefile to multi-part polygon
        
        Args:
            input_path: Path to input zipped shapefile
            output_path: Path where to save the output (optional)
            timeout: Request timeout in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if input file exists
            if not os.path.exists(input_path):
                print(f"Input file not found: {input_path}")
                return False
            
            # Default output path if not provided
            if output_path is None:
                base_name = Path(input_path).stem
                output_path = f"{base_name}_multipart.zip"
            
            # Send conversion request
            with open(input_path, 'rb') as f:
                files = {'shapefile': f}
                response = self.session.post(
                    f"{self.api_url}/api/convert",
                    files=files,
                    timeout=timeout,
                    stream=True
                )
            
            # Check response
            if response.status_code == 200:
                # Save the output file
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print(f"Conversion successful! Output saved to: {output_path}")
                return True
            else:
                print(f"Conversion failed: {response.status_code}")
                try:
                    error_msg = response.json()
                    print(f"Error details: {error_msg}")
                except:
                    print(response.text)
                return False
                
        except requests.exceptions.Timeout:
            print(f"Request timed out after {timeout} seconds")
            return False
        except Exception as e:
            print(f"Error during conversion: {e}")
            return False
    
    def batch_convert(self, input_folder: str, output_folder: str) -> Dict[str, bool]:
        """
        Convert multiple shapefiles in batch
        
        Args:
            input_folder: Folder containing zipped shapefiles
            output_folder: Folder to save converted files
            
        Returns:
            Dictionary with filename as key and success status as value
        """
        results = {}
        
        # Create output folder if it doesn't exist
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        
        # Process each zip file in the input folder
        for filename in os.listdir(input_folder):
            if filename.endswith('.zip'):
                input_path = os.path.join(input_folder, filename)
                output_path = os.path.join(
                    output_folder, 
                    f"{Path(filename).stem}_multipart.zip"
                )
                
                print(f"\nProcessing: {filename}")
                success = self.convert_to_multipart(input_path, output_path)
                results[filename] = success
        
        return results


def create_sample_shapefile(output_path: str = "sample_shapefile.zip"):
    """
    Create a sample shapefile for testing
    This is a placeholder - in real usage, you would have actual shapefiles
    """
    print(f"Note: Use actual shapefiles with 3 polygons and SUP_HA field")
    print(f"This function is just a placeholder for demonstration")


def test_single_shapefile(shapefile_path: str):
    """Test a single shapefile with the API"""

    # Initialize client
    client = ShapefileConverterClient("http://localhost:3000")

    # Check if API is running
    if not client.health_check():
        print("API is not running. Please start the Node.js server first.")
        print("Run: npm install && npm start")
        return False

    print("API is running and healthy!")

    # Check if file exists
    if not os.path.exists(shapefile_path):
        print(f"Error: File not found: {shapefile_path}")
        return False

    # Example 1: Analyze a shapefile
    print(f"\n--- Analyzing Shapefile: {os.path.basename(shapefile_path)} ---")
    analysis = client.analyze_shapefile(shapefile_path)
    if analysis:
        print("Analysis results:")
        print(f"  Feature count: {analysis['featureCount']}")
        print(f"  Ready for conversion: {analysis['ready']}")
        for feature in analysis['features']:
            print(f"  Feature {feature['index']}: {feature['type']}, "
                  f"Area: {feature['area_hectares']} ha")
    else:
        print("  Analysis failed!")
        return False

    # Example 2: Convert the shapefile (only if analysis was successful)
    if analysis and analysis['ready']:
        print(f"\n--- Converting Shapefile: {os.path.basename(shapefile_path)} ---")
        output_filename = f"{Path(shapefile_path).stem}_converted.zip"
        output_path = f"output/{output_filename}"

        success = client.convert_to_multipart(
            input_path=shapefile_path,
            output_path=output_path
        )

        if success:
            print("✓ Conversion completed successfully!")
            print(f"  Output file: {output_path}")
        else:
            print("✗ Conversion failed!")
            return False
    else:
        print("\n--- Skipping Conversion ---")
        print("  Shapefile is not ready for conversion (doesn't have exactly 3 polygons)")

    print("\n--- Test Summary ---")
    print(f"  File tested: {os.path.basename(shapefile_path)}")
    print(f"  Analysis: {'✓ Success' if analysis else '✗ Failed'}")
    if analysis:
        print(f"  Features found: {analysis['featureCount']}")
        print(f"  Ready for conversion: {'✓ Yes' if analysis['ready'] else '✗ No'}")
        if analysis['ready']:
            print(f"  Conversion: {'✓ Success' if success else '✗ Failed'}")

    return True


def main():
    """Main function with different test options"""

    # Available test files - you can easily change which one to test
    test_files = {
        'heron': r"C:\Users\<USER>\Downloads\Heron_test.zip",
        'habitat1': r"C:\Users\<USER>\Downloads\Habitats_fauniques.zip",
        'habitat2': r"C:\Users\<USER>\Downloads\Habitats_fauniques (3).zip"
    }

    # Choose which file to test (change this to test different files)
    selected_file = 'heron'  # Change this to 'habitat1' or 'habitat2' to test other files

    if selected_file in test_files:
        shapefile_path = test_files[selected_file]
        print(f"Testing with: {selected_file} -> {os.path.basename(shapefile_path)}")
        test_single_shapefile(shapefile_path)
    else:
        print(f"Unknown file selection: {selected_file}")
        print(f"Available options: {list(test_files.keys())}")


# Additional utility functions

def validate_shapefile_structure(zip_path: str) -> bool:
    """
    Validate that a zip file contains the required shapefile components
    
    Args:
        zip_path: Path to the zipped shapefile
        
    Returns:
        True if valid shapefile structure, False otherwise
    """
    required_extensions = {'.shp', '.shx', '.dbf'}
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zf:
            files = zf.namelist()
            extensions = {Path(f).suffix.lower() for f in files}
            return required_extensions.issubset(extensions)
    except Exception as e:
        print(f"Error validating shapefile: {e}")
        return False


def extract_metadata(converted_zip: str) -> Optional[Dict[str, Any]]:
    """
    Extract metadata from converted shapefile
    
    Args:
        converted_zip: Path to the converted multipart shapefile
        
    Returns:
        Metadata dictionary or None if failed
    """
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Extract the zip file
            with zipfile.ZipFile(converted_zip, 'r') as zf:
                zf.extractall(temp_dir)
            
            # Read the .prj file if exists for projection info
            prj_files = list(Path(temp_dir).glob('*.prj'))
            projection = None
            if prj_files:
                with open(prj_files[0], 'r') as f:
                    projection = f.read().strip()
            
            # Basic metadata
            metadata = {
                'file_size': os.path.getsize(converted_zip),
                'projection': projection,
                'files': os.listdir(temp_dir)
            }
            
            return metadata
            
    except Exception as e:
        print(f"Error extracting metadata: {e}")
        return None


if __name__ == "__main__":
    main()